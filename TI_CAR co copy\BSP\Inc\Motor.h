#ifndef __Motor_h
#define __Motor_h

#include "SysConfig.h"

/**
 * @brief 电机方向
 * 
 */
typedef enum
{
    DIRC_NONE = 0,
    DIRC_FOWARD, //正转
    DIRC_BACKWARD //反转
} Motor_DIRC_Def_t;

/**
 * @brief 电机结构体定义(TB6612)
 *
 */
typedef struct
{
    GPTIMER_Regs *Motor_PWM_TIMX; //PWM对应的定时器
    __IO int Motor_PWM_CH; //PWM通道
    __IO uint32_t Motor_IN1_Pin; //电机IN1控制引脚
    __IO uint32_t Motor_IN2_Pin; //电机IN2控制引脚
    int16_t *Motor_Encoder_Addr; //电机编码值地址
    Motor_DIRC_Def_t Motor_Dirc; //电机当前正转还是反转
    PID_IQ_Def_t Motor_PID_Instance; //PID实例
} MOTOR_Def_t;

extern MOTOR_Def_t Motor_Font_Left; //左前轮
extern MOTOR_Def_t Motor_Font_Right; //左后轮
extern MOTOR_Def_t Motor_Back_Left; //右前轮
extern MOTOR_Def_t Motor_Back_Right; //右后轮

void Motor_Start(void);
void Motor_Enable(bool enable); //TB6612 STBY控制
bool Motor_SetDuty(MOTOR_Def_t *Motor, float value);
bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time);
void Motor_Test_TB6612(void); //TB6612测试函数

#endif