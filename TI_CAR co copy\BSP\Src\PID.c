#include "PID.h"

#define PID_DYNAMIC_KI_INDEX 0.3f //变速积分的计算值 值越小 KI作用越小
#define PID_KI_MAX_VALUE     50.0f //积分项最大值
#define PID_OUT_MAX_VALUE    100.0f //PID 输出最大值

/**
 * @brief 初始化PID项
 * 
 * @param pid PID对象
 */
void PID_Init(PID_Def_t *pid)
{
    // pid->Kp = 0;
    // pid->Ki = 0;
    // pid->Kd = 0;
    pid->Acutal_Now = 0;
    pid->Acutal_Last = 0;
    pid->Out = 0;
    pid->Target = 0;
    pid->Dif_Out = 0;
    pid->Err_Int = 0;
    pid->Err_Last = 0;
    pid->Err_Now = 0;
}

/**
 * @brief PID处理逻辑
 * 
 * @param pid 处理PID对象
 */
void PID_Prosc(PID_Def_t *pid)
{
    pid->Err_Last = pid->Err_Now;
    pid->Acutal_Last = pid->Acutal_Now;
    pid->Err_Now = pid->Target - pid->Acutal_Now;

    //变速积分 误差越大 比例越小
    float C = 1 / (PID_DYNAMIC_KI_INDEX * fabs(pid->Err_Now) + 1);
    pid->Err_Int += C * pid->Err_Now;
    if (pid->Err_Int > PID_KI_MAX_VALUE) pid->Err_Int = PID_KI_MAX_VALUE;
    else if (pid->Err_Int < -PID_KI_MAX_VALUE) pid->Err_Int = -PID_KI_MAX_VALUE;

    //微分先行 处理数据突变导致Kd有尖刺
    pid->Dif_Out = -pid->Kd * (pid->Acutal_Now - pid->Acutal_Last);

    pid->Out = pid->Kp * pid->Err_Now + pid->Ki * pid->Err_Int + pid->Dif_Out;

    if (pid->Out > PID_OUT_MAX_VALUE) pid->Out = PID_OUT_MAX_VALUE;
    if (pid->Out < -PID_OUT_MAX_VALUE) pid->Out = -PID_OUT_MAX_VALUE;
}

/**
 * @brief 设置PID参数
 * 
 * @param pid PID对象
 * @param kp 比例系数
 * @param ki 积分系数  
 * @param kd 微分系数
 */
void PID_SetParams(PID_Def_t *pid, float kp, float ki, float kd)
{
    pid->Kp = kp;
    pid->Ki = ki;
    pid->Kd = kd;
}