******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 17:52:20 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000074f9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000098b0  00016750  R  X
  SRAM                  20200000   00008000  000006c1  0000793f  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000098b0   000098b0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00008090   00008090    r-x .text
  00008150    00008150    000016d0   000016d0    r-- .rodata
  00009820    00009820    00000090   00000090    r-- .cinit
20200000    20200000    000004c2   00000000    rw-
  20200000    20200000    00000323   00000000    rw- .bss
  20200324    20200324    0000019e   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00008090     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    000002bc     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000013a8    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001620    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  00001858    0000022c     MPU6050.o (.text.Read_Quad)
                  00001a84    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001cb0    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001ed0    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  000020c4    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000022a0    000001b0     Task.o (.text.Task_Start)
                  00002450    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  000025f0    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00002782    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002784    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  0000290c    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002a84    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002bf4    00000168     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002d5c    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002ea0    0000013c     Tracker.o (.text.Tracker_Read)
                  00002fdc    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00003118    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  0000324c    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003380    00000130     OLED.o (.text.OLED_ShowChar)
                  000034b0    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  000035e0    00000128     inv_mpu.o (.text.mpu_init)
                  00003708    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  0000382c    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003950    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003a70    00000110     OLED.o (.text.OLED_Init)
                  00003b80    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003c8c    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003d94    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00003e98    00000104     Task_App.o (.text.Task_Motor_PID)
                  00003f9c    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  0000409c    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00004188    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0000426c    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00004350    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000442c    000000dc     Task_App.o (.text.Task_OLED)
                  00004508    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000045e0    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000046b8    000000d4     inv_mpu.o (.text.set_int_enable)
                  0000478c    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  0000485c    000000cc     Motor.o (.text.Motor_Start)
                  00004928    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  000049ec    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004ab0    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004b6c    000000b8     Motor.o (.text.Motor_SetDirc)
                  00004c24    000000b8     Motor.o (.text.Motor_SetDuty)
                  00004cdc    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004d94    000000b4     Task.o (.text.Task_Add)
                  00004e48    000000b4     main.o (.text.main)
                  00004efc    000000b0     Task_App.o (.text.Task_Init)
                  00004fac    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00005058    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00005104    000000a4     Motor.o (.text.Motor_GetSpeed)
                  000051a8    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  0000524a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  0000524c    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000052ec    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00005388    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00005420    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  000054b8    00000096     MPU6050.o (.text.inv_row_2_scale)
                  0000554e    00000002     --HOLE-- [fill = 0]
                  00005550    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  000055dc    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00005668    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000056f4    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005780    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005804    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005888    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000590a    00000002     --HOLE-- [fill = 0]
                  0000590c    00000080     Task_App.o (.text.Task_Serial)
                  0000598c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005a08    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005a7c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00005a80    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005af4    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005b68    00000070     Serial.o (.text.MyPrintf_DMA)
                  00005bd8    0000006e     OLED.o (.text.OLED_ShowString)
                  00005c46    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005cb0    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005d18    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00005d7e    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005de4    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005e48    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00005eac    00000064     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00005f10    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005f72    00000002     --HOLE-- [fill = 0]
                  00005f74    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005fd6    00000002     --HOLE-- [fill = 0]
                  00005fd8    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00006038    00000060     Key_Led.o (.text.Key_Read)
                  00006098    00000060     Task_App.o (.text.Task_IdleFunction)
                  000060f8    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00006158    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  000061b8    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00006216    00000002     --HOLE-- [fill = 0]
                  00006218    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00006274    0000005c     Task_App.o (.text.Task_Tracker)
                  000062d0    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000632c    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00006388    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  000063e4    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  0000643c    00000058     Serial.o (.text.Serial_Init)
                  00006494    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000064ec    00000058            : _printfi.c.obj (.text._pconv_f)
                  00006544    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000659a    00000002     --HOLE-- [fill = 0]
                  0000659c    00000054     Interrupt.o (.text.Interrupt_Init)
                  000065f0    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00006642    00000002     --HOLE-- [fill = 0]
                  00006644    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00006694    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000066e4    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006734    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00006780    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000067cc    0000004c     OLED.o (.text.OLED_Printf)
                  00006818    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006862    00000002     --HOLE-- [fill = 0]
                  00006864    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000068ac    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  000068f4    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  0000693c    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00006984    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000069c8    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00006a0c    00000044     Task_App.o (.text.Task_Key)
                  00006a50    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00006a94    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00006ad8    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00006b1c    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00006b5e    00000002     --HOLE-- [fill = 0]
                  00006b60    00000040                            : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00006ba0    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00006be0    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00006c20    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00006c60    0000003e     Task.o (.text.Task_CMP)
                  00006c9e    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00006cdc    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006d18    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006d54    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006d90    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00006dcc    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00006e08    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00006e44    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00006e80    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00006ebc    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00006ef8    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00006f32    00000002     --HOLE-- [fill = 0]
                  00006f34    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00006f6e    00000002     --HOLE-- [fill = 0]
                  00006f70    00000038     Task_App.o (.text.Task_LED)
                  00006fa8    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006fe0    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007014    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007048    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000707c    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  000070b0    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  000070e2    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00007114    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00007144    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00007174    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  000071a4    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000071d4    00000030            : vsnprintf.c.obj (.text._outs)
                  00007204    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00007234    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00007264    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00007290    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  000072bc    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000072e8    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00007314    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  0000733e    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007366    00000028     OLED.o (.text.DL_Common_updateReg)
                  0000738e    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000073b6    00000002     --HOLE-- [fill = 0]
                  000073b8    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  000073e0    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00007408    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00007430    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00007458    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00007480    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  000074a8    00000028     SysTick.o (.text.SysTick_Increasment)
                  000074d0    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000074f8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00007520    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00007546    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  0000756c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00007592    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000075b8    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  000075dc    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00007600    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00007622    00000002     --HOLE-- [fill = 0]
                  00007624    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007644    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007664    00000020     SysTick.o (.text.Delay)
                  00007684    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  000076a4    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000076c2    00000002     --HOLE-- [fill = 0]
                  000076c4    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000076e2    00000002     --HOLE-- [fill = 0]
                  000076e4    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007700    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  0000771c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007738    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007754    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007770    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  0000778c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000077a8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000077c4    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000077e0    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  000077fc    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00007818    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007834    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00007850    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000786c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007888    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000078a4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000078c0    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000078dc    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  000078f8    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00007910    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00007928    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00007940    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00007958    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007970    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007988    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  000079a0    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  000079b8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000079d0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000079e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00007a00    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00007a18    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00007a30    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00007a48    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00007a60    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00007a78    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00007a90    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00007aa8    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00007ac0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00007ad8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00007af0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00007b08    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00007b20    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00007b38    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00007b50    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00007b68    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00007b80    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00007b98    00000018     OLED.o (.text.DL_I2C_reset)
                  00007bb0    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00007bc8    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00007be0    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00007bf8    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00007c10    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00007c28    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00007c40    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00007c58    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00007c70    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00007c88    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00007ca0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00007cb8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007cd0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00007ce8    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00007d00    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00007d18    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00007d30    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00007d48    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00007d60    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00007d78    00000018            : vsprintf.c.obj (.text._outs)
                  00007d90    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00007da6    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  00007dbc    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00007dd2    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00007de8    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  00007dfe    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00007e14    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00007e2a    00000016     SysTick.o (.text.SysGetTick)
                  00007e40    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00007e56    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00007e6a    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00007e7e    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00007e92    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00007ea6    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00007eba    00000002     --HOLE-- [fill = 0]
                  00007ebc    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00007ed0    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00007ee4    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00007ef8    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00007f0c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00007f20    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00007f34    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00007f48    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00007f5c    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00007f70    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00007f84    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00007f98    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00007faa    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00007fbc    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00007fce    00000002     --HOLE-- [fill = 0]
                  00007fd0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00007fe0    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00007ff0    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00008000    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00008010    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000801e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  0000802c    0000000e     MPU6050.o (.text.tap_cb)
                  0000803a    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00008048    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00008054    0000000c     SysTick.o (.text.Sys_GetTick)
                  00008060    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000806a    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00008074    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00008084    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000808e    00000002     --HOLE-- [fill = 0]
                  00008090    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  000080a0    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  000080aa    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000080b4    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  000080be    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  000080c8    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  000080d8    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  000080e2    0000000a     MPU6050.o (.text.android_orient_cb)
                  000080ec    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  000080f4    00000008     Interrupt.o (.text.SysTick_Handler)
                  000080fc    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00008104    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000810c    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008112    00000002     --HOLE-- [fill = 0]
                  00008114    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00008124    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  0000812a    00000006            : exit.c.obj (.text:abort)
                  00008130    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00008134    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00008138    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000813c    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  0000814c    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00009820    00000090     
                  00009820    0000006b     (.cinit..data.load) [load image, compression = lzss]
                  0000988b    00000001     --HOLE-- [fill = 0]
                  0000988c    0000000c     (__TI_handler_table)
                  00009898    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000098a0    00000010     (__TI_cinit_table)

.rodata    0    00008150    000016d0     
                  00008150    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00008d46    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00009336    00000228     OLED_Font.o (.rodata.asc2_0806)
                  0000955e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00009560    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009661    00000007     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00009668    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  000096a8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000096d0    00000028     inv_mpu.o (.rodata.test)
                  000096f8    0000001e     inv_mpu.o (.rodata.reg)
                  00009716    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00009718    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00009730    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00009748    00000014     Task_App.o (.rodata.str1.11952760121962574671.1)
                  0000975c    00000014     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00009770    00000014     Task_App.o (.rodata.str1.492715258893803702.1)
                  00009784    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00009795    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000097a6    00000011     Task_App.o (.rodata.str1.3850258909703972507.1)
                  000097b7    00000011     Task_App.o (.rodata.str1.5883415095785080416.1)
                  000097c8    0000000c     inv_mpu.o (.rodata.hw)
                  000097d4    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  000097de    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  000097e0    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  000097e8    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  000097f0    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  000097f8    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  000097fe    00000005     Task_App.o (.rodata.str1.11683036942922059812.1)
                  00009803    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00009807    00000004     Task_App.o (.rodata.str1.16020955549137178199.1)
                  0000980b    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  0000980e    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00009811    0000000f     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000323     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    00000010     (.common:quat)
                  20200300    00000006     (.common:Data_Accel)
                  20200306    00000006     (.common:Data_Gyro)
                  2020030c    00000004     (.common:Data_Pitch)
                  20200310    00000004     (.common:Data_Roll)
                  20200314    00000004     (.common:Data_Yaw)
                  20200318    00000004     (.common:ExISR_Flag)
                  2020031c    00000004     (.common:sensor_timestamp)
                  20200320    00000002     (.common:sensors)
                  20200322    00000001     (.common:more)

.data      0    20200324    0000019e     UNINITIALIZED
                  20200324    00000044     Motor.o (.data.Motor_Back_Left)
                  20200368    00000044     Motor.o (.data.Motor_Back_Right)
                  202003ac    00000044     Motor.o (.data.Motor_Font_Left)
                  202003f0    00000044     Motor.o (.data.Motor_Font_Right)
                  20200434    0000002c     inv_mpu.o (.data.st)
                  20200460    00000010     Task_App.o (.data.Motor)
                  20200470    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  20200480    0000000e     MPU6050.o (.data.hal)
                  2020048e    00000009     MPU6050.o (.data.gyro_orientation)
                  20200497    00000001     Task_App.o (.data.Flag_LED)
                  20200498    00000008     Task_App.o (.data.Data_MotorEncoder)
                  202004a0    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004a8    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004ac    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004b0    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004b4    00000004     SysTick.o (.data.delayTick)
                  202004b8    00000004     SysTick.o (.data.uwTick)
                  202004bc    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004be    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004bf    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  202004c0    00000001     Task.o (.data.Task_Num)
                  202004c1    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3490    126       0      
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         180     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3678    318       0      
                                                               
    .\APP\Src\
       Task_App.o                     1140    128       44     
       Interrupt.o                    622     0         6      
    +--+------------------------------+-------+---------+---------+
       Total:                         1762    128       50     
                                                               
    .\BSP\Src\
       MPU6050.o                      2472    0         70     
       OLED_Font.o                    0       2072      0      
       OLED.o                         1854    0         0      
       Motor.o                        804     0         272    
       Serial.o                       404     0         512    
       Task.o                         674     0         241    
       PID_IQMath.o                   402     0         0      
       Tracker.o                      338     0         0      
       Key_Led.o                      118     0         0      
       SysTick.o                      106     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         7172    2072      1103   
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1112    0         0      
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                      48      0         0      
       _IQNdiv.o                      24      0         0      
       _IQNmpy.o                      24      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         0      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8356    355       4      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2984    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       143       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   32874   6160      1729   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000098a0 records: 2, size/record: 8, table size: 16
	.data: load addr=00009820, load size=0000006b bytes, run addr=20200324, run size=0000019e bytes, compression=lzss
	.bss: load addr=00009898, load size=00000008 bytes, run addr=20200000, run size=00000323 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000988c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000025f1     00008074     00008072   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00004189     00008090     0000808c   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             000080a8          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             000080bc          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             000080f2          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00008128          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003b81     000080c8     000080c6   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   000025fb     00008114     00008110   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00008136          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000074f9     0000813c     00008138   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00005a7d  ADC0_IRQHandler                      
00005a7d  ADC1_IRQHandler                      
00005a7d  AES_IRQHandler                       
00008130  C$$EXIT                              
00005a7d  CANFD0_IRQHandler                    
00005a7d  DAC0_IRQHandler                      
00008061  DL_Common_delayCycles                
00006735  DL_DMA_initChannel                   
000061b9  DL_I2C_fillControllerTXFIFO          
00006d91  DL_I2C_flushControllerTXFIFO         
00007593  DL_I2C_setClockConfig                
00004351  DL_SYSCTL_configSYSPLL               
00005de5  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006985  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003d95  DL_Timer_initFourCCPWMMode           
00007889  DL_Timer_setCaptCompUpdateMethod     
00007c59  DL_Timer_setCaptureCompareOutCtl     
00007fe1  DL_Timer_setCaptureCompareValue      
000078a5  DL_Timer_setClockConfig              
00006865  DL_UART_init                         
00007f99  DL_UART_setClockConfig               
00005a7d  DMA_IRQHandler                       
20200300  Data_Accel                           
20200306  Data_Gyro                            
20200498  Data_MotorEncoder                    
202004a8  Data_Motor_TarSpeed                  
2020030c  Data_Pitch                           
20200310  Data_Roll                            
202004a0  Data_Tracker_Input                   
202004ac  Data_Tracker_Offset                  
20200314  Data_Yaw                             
00005a7d  Default_Handler                      
00007665  Delay                                
20200318  ExISR_Flag                           
20200497  Flag_LED                             
202004be  Flag_MPU6050_Ready                   
00005a7d  GROUP0_IRQHandler                    
00002bf5  GROUP1_IRQHandler                    
00008131  HOSTexit                             
00005a7d  HardFault_Handler                    
00005a7d  I2C0_IRQHandler                      
00005a7d  I2C1_IRQHandler                      
00005c47  I2C_OLED_Clear                       
00006e09  I2C_OLED_Set_Pos                     
00005389  I2C_OLED_WR_Byte                     
00005fd9  I2C_OLED_i2c_sda_unlock              
0000659d  Interrupt_Init                       
00006039  Key_Read                             
00002d5d  MPU6050_Init                         
20200460  Motor                                
20200324  Motor_Back_Left                      
20200368  Motor_Back_Right                     
202003ac  Motor_Font_Left                      
202003f0  Motor_Font_Right                     
00005105  Motor_GetSpeed                       
00004c25  Motor_SetDuty                        
0000485d  Motor_Start                          
00005b69  MyPrintf_DMA                         
00005a7d  NMI_Handler                          
00003a71  OLED_Init                            
000067cd  OLED_Printf                          
00003381  OLED_ShowChar                        
00005bd9  OLED_ShowString                      
00007315  PID_IQ_Init                          
00003709  PID_IQ_Prosc                         
000069c9  PID_IQ_SetParams                     
00005a7d  PendSV_Handler                       
00005a7d  RTC_IRQHandler                       
00001859  Read_Quad                            
00008139  Reset_Handler                        
00005a7d  SPI0_IRQHandler                      
00005a7d  SPI1_IRQHandler                      
00005a7d  SVC_Handler                          
00007145  SYSCFG_DL_DMA_CH_RX_init             
00007d19  SYSCFG_DL_DMA_CH_TX_init             
00008049  SYSCFG_DL_DMA_init                   
000010ed  SYSCFG_DL_GPIO_init                  
000063e5  SYSCFG_DL_I2C_MPU6050_init           
00005e49  SYSCFG_DL_I2C_OLED_init              
00005551  SYSCFG_DL_MotorBack_init             
000055dd  SYSCFG_DL_MotorFront_init            
00006219  SYSCFG_DL_SYSCTL_init                
00007ff1  SYSCFG_DL_SYSTICK_init               
00005781  SYSCFG_DL_UART0_init                 
00007265  SYSCFG_DL_init                       
0000524d  SYSCFG_DL_initPower                  
0000643d  Serial_Init                          
20200000  Serial_RxData                        
00007e2b  SysGetTick                           
000080f5  SysTick_Handler                      
000074a9  SysTick_Increasment                  
00008055  Sys_GetTick                          
00005a7d  TIMA0_IRQHandler                     
00005a7d  TIMA1_IRQHandler                     
00005a7d  TIMG0_IRQHandler                     
00005a7d  TIMG12_IRQHandler                    
00005a7d  TIMG6_IRQHandler                     
00005a7d  TIMG7_IRQHandler                     
00005a7d  TIMG8_IRQHandler                     
00007fab  TI_memcpy_small                      
0000803b  TI_memset_small                      
00004d95  Task_Add                             
00006099  Task_IdleFunction                    
00004efd  Task_Init                            
00006a0d  Task_Key                             
00006f71  Task_LED                             
00003e99  Task_Motor_PID                       
0000442d  Task_OLED                            
0000590d  Task_Serial                          
000022a1  Task_Start                           
00006275  Task_Tracker                         
00002ea1  Tracker_Read                         
00005a7d  UART0_IRQHandler                     
00005a7d  UART1_IRQHandler                     
00005a7d  UART2_IRQHandler                     
00005a7d  UART3_IRQHandler                     
00007d31  _IQ24div                             
00007d49  _IQ24mpy                             
00007175  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000098a0  __TI_CINIT_Base                      
000098b0  __TI_CINIT_Limit                     
000098b0  __TI_CINIT_Warm                      
0000988c  __TI_Handler_Table_Base              
00009898  __TI_Handler_Table_Limit             
00006ebd  __TI_auto_init_nobinit_nopinit       
0000598d  __TI_decompress_lzss                 
00007fbd  __TI_decompress_none                 
00006495  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00007e41  __TI_zero_init_nomemset              
000025fb  __adddf3                             
000045eb  __addsf3                             
00009560  __aeabi_ctype_table_                 
00009560  __aeabi_ctype_table_C                
00005a81  __aeabi_d2f                          
00006819  __aeabi_d2iz                         
00006b1d  __aeabi_d2uiz                        
000025fb  __aeabi_dadd                         
00005f11  __aeabi_dcmpeq                       
00005f4d  __aeabi_dcmpge                       
00005f61  __aeabi_dcmpgt                       
00005f39  __aeabi_dcmple                       
00005f25  __aeabi_dcmplt                       
00003b81  __aeabi_ddiv                         
00004189  __aeabi_dmul                         
000025f1  __aeabi_dsub                         
202004b0  __aeabi_errno                        
000080fd  __aeabi_errno_addr                   
00006ba1  __aeabi_f2d                          
00006fa9  __aeabi_f2iz                         
000045eb  __aeabi_fadd                         
00005f75  __aeabi_fcmpeq                       
00005fb1  __aeabi_fcmpge                       
00005fc5  __aeabi_fcmpgt                       
00005f9d  __aeabi_fcmple                       
00005f89  __aeabi_fcmplt                       
00005889  __aeabi_fdiv                         
00005669  __aeabi_fmul                         
000045e1  __aeabi_fsub                         
000072bd  __aeabi_i2d                          
00006e45  __aeabi_i2f                          
00006545  __aeabi_idiv                         
00002783  __aeabi_idiv0                        
00006545  __aeabi_idivmod                      
0000524b  __aeabi_ldiv0                        
000076c5  __aeabi_llsl                         
000075dd  __aeabi_lmul                         
00008105  __aeabi_memcpy                       
00008105  __aeabi_memcpy4                      
00008105  __aeabi_memcpy8                      
00008011  __aeabi_memset                       
00008011  __aeabi_memset4                      
00008011  __aeabi_memset8                      
000074d1  __aeabi_ui2f                         
00006b61  __aeabi_uidiv                        
00006b61  __aeabi_uidivmod                     
00007f49  __aeabi_uldivmod                     
000076c5  __ashldi3                            
ffffffff  __binit__                            
00005cb1  __cmpdf2                             
00006ef9  __cmpsf2                             
00003b81  __divdf3                             
00005889  __divsf3                             
00005cb1  __eqdf2                              
00006ef9  __eqsf2                              
00006ba1  __extendsfdf2                        
00006819  __fixdfsi                            
00006fa9  __fixsfsi                            
00006b1d  __fixunsdfsi                         
000072bd  __floatsidf                          
00006e45  __floatsisf                          
000074d1  __floatunsisf                        
00005a09  __gedf2                              
00006e81  __gesf2                              
00005a09  __gtdf2                              
00006e81  __gtsf2                              
00005cb1  __ledf2                              
00006ef9  __lesf2                              
00005cb1  __ltdf2                              
00006ef9  __ltsf2                              
UNDEFED   __mpu_init                           
00004189  __muldf3                             
000075dd  __muldi3                             
00006f35  __muldsi3                            
00005669  __mulsf3                             
00005cb1  __nedf2                              
00006ef9  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000025f1  __subdf3                             
000045e1  __subsf3                             
00005a81  __truncdfsf2                         
000051a9  __udivmoddi4                         
000074f9  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
0000814d  _system_pre_init                     
0000812b  abort                                
00009336  asc2_0806                            
00008d46  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002785  atan2                                
00002785  atan2l                               
00000df5  atanl                                
00006be1  atoi                                 
ffffffff  binit                                
202004b4  delayTick                            
000068ad  dmp_enable_6x_lp_quat                
000013a9  dmp_enable_feature                   
000060f9  dmp_enable_gyro_cal                  
000068f5  dmp_enable_lp_quat                   
000078dd  dmp_load_motion_driver_firmware      
00001ed1  dmp_read_fifo                        
00007f5d  dmp_register_android_orient_cb       
00007f71  dmp_register_tap_cb                  
00005421  dmp_set_fifo_rate                    
0000290d  dmp_set_orientation                  
00006a51  dmp_set_shake_reject_thresh          
000070b1  dmp_set_shake_reject_time            
000070e3  dmp_set_shake_reject_timeout         
00005d7f  dmp_set_tap_axes                     
00006a95  dmp_set_tap_count                    
00001621  dmp_set_tap_thresh                   
00007205  dmp_set_tap_time                     
00007235  dmp_set_tap_time_multi               
202004c1  enable_group1_irq                    
000062d1  frexp                                
000062d1  frexpl                               
000097c8  hw                                   
00000000  interruptVectors                     
00004509  ldexp                                
00004509  ldexpl                               
00004e49  main                                 
00007601  memccpy                              
00007685  memcmp                               
20200322  more                                 
00005ead  mpu6050_i2c_sda_unlock               
00004ab1  mpu_configure_fifo                   
00005af5  mpu_get_accel_fsr                    
00006159  mpu_get_gyro_fsr                     
0000707d  mpu_get_sample_rate                  
000035e1  mpu_init                             
0000382d  mpu_load_firmware                    
00003f9d  mpu_lp_accel_mode                    
00003c8d  mpu_read_fifo_stream                 
00004fad  mpu_read_mem                         
00001a85  mpu_reset_fifo                       
0000426d  mpu_set_accel_fsr                    
00002451  mpu_set_bypass                       
00004cdd  mpu_set_dmp_state                    
00004929  mpu_set_gyro_fsr                     
000052ed  mpu_set_int_latched                  
0000478d  mpu_set_lpf                          
0000409d  mpu_set_sample_rate                  
000034b1  mpu_set_sensors                      
00005059  mpu_write_mem                        
00003119  mspm0_i2c_read                       
000049ed  mspm0_i2c_write                      
0000324d  qsort                                
202002f0  quat                                 
000096f8  reg                                  
00004509  scalbn                               
00004509  scalbnl                              
2020031c  sensor_timestamp                     
20200320  sensors                              
00002a85  sqrt                                 
00002a85  sqrtl                                
000096d0  test                                 
202004b8  uwTick                               
00006c21  vsnprintf                            
000072e9  vsprintf                             
00008001  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  SYSCFG_DL_GPIO_init                  
000013a9  dmp_enable_feature                   
00001621  dmp_set_tap_thresh                   
00001859  Read_Quad                            
00001a85  mpu_reset_fifo                       
00001ed1  dmp_read_fifo                        
000022a1  Task_Start                           
00002451  mpu_set_bypass                       
000025f1  __aeabi_dsub                         
000025f1  __subdf3                             
000025fb  __adddf3                             
000025fb  __aeabi_dadd                         
00002783  __aeabi_idiv0                        
00002785  atan2                                
00002785  atan2l                               
0000290d  dmp_set_orientation                  
00002a85  sqrt                                 
00002a85  sqrtl                                
00002bf5  GROUP1_IRQHandler                    
00002d5d  MPU6050_Init                         
00002ea1  Tracker_Read                         
00003119  mspm0_i2c_read                       
0000324d  qsort                                
00003381  OLED_ShowChar                        
000034b1  mpu_set_sensors                      
000035e1  mpu_init                             
00003709  PID_IQ_Prosc                         
0000382d  mpu_load_firmware                    
00003a71  OLED_Init                            
00003b81  __aeabi_ddiv                         
00003b81  __divdf3                             
00003c8d  mpu_read_fifo_stream                 
00003d95  DL_Timer_initFourCCPWMMode           
00003e99  Task_Motor_PID                       
00003f9d  mpu_lp_accel_mode                    
0000409d  mpu_set_sample_rate                  
00004189  __aeabi_dmul                         
00004189  __muldf3                             
0000426d  mpu_set_accel_fsr                    
00004351  DL_SYSCTL_configSYSPLL               
0000442d  Task_OLED                            
00004509  ldexp                                
00004509  ldexpl                               
00004509  scalbn                               
00004509  scalbnl                              
000045e1  __aeabi_fsub                         
000045e1  __subsf3                             
000045eb  __addsf3                             
000045eb  __aeabi_fadd                         
0000478d  mpu_set_lpf                          
0000485d  Motor_Start                          
00004929  mpu_set_gyro_fsr                     
000049ed  mspm0_i2c_write                      
00004ab1  mpu_configure_fifo                   
00004c25  Motor_SetDuty                        
00004cdd  mpu_set_dmp_state                    
00004d95  Task_Add                             
00004e49  main                                 
00004efd  Task_Init                            
00004fad  mpu_read_mem                         
00005059  mpu_write_mem                        
00005105  Motor_GetSpeed                       
000051a9  __udivmoddi4                         
0000524b  __aeabi_ldiv0                        
0000524d  SYSCFG_DL_initPower                  
000052ed  mpu_set_int_latched                  
00005389  I2C_OLED_WR_Byte                     
00005421  dmp_set_fifo_rate                    
00005551  SYSCFG_DL_MotorBack_init             
000055dd  SYSCFG_DL_MotorFront_init            
00005669  __aeabi_fmul                         
00005669  __mulsf3                             
00005781  SYSCFG_DL_UART0_init                 
00005889  __aeabi_fdiv                         
00005889  __divsf3                             
0000590d  Task_Serial                          
0000598d  __TI_decompress_lzss                 
00005a09  __gedf2                              
00005a09  __gtdf2                              
00005a7d  ADC0_IRQHandler                      
00005a7d  ADC1_IRQHandler                      
00005a7d  AES_IRQHandler                       
00005a7d  CANFD0_IRQHandler                    
00005a7d  DAC0_IRQHandler                      
00005a7d  DMA_IRQHandler                       
00005a7d  Default_Handler                      
00005a7d  GROUP0_IRQHandler                    
00005a7d  HardFault_Handler                    
00005a7d  I2C0_IRQHandler                      
00005a7d  I2C1_IRQHandler                      
00005a7d  NMI_Handler                          
00005a7d  PendSV_Handler                       
00005a7d  RTC_IRQHandler                       
00005a7d  SPI0_IRQHandler                      
00005a7d  SPI1_IRQHandler                      
00005a7d  SVC_Handler                          
00005a7d  TIMA0_IRQHandler                     
00005a7d  TIMA1_IRQHandler                     
00005a7d  TIMG0_IRQHandler                     
00005a7d  TIMG12_IRQHandler                    
00005a7d  TIMG6_IRQHandler                     
00005a7d  TIMG7_IRQHandler                     
00005a7d  TIMG8_IRQHandler                     
00005a7d  UART0_IRQHandler                     
00005a7d  UART1_IRQHandler                     
00005a7d  UART2_IRQHandler                     
00005a7d  UART3_IRQHandler                     
00005a81  __aeabi_d2f                          
00005a81  __truncdfsf2                         
00005af5  mpu_get_accel_fsr                    
00005b69  MyPrintf_DMA                         
00005bd9  OLED_ShowString                      
00005c47  I2C_OLED_Clear                       
00005cb1  __cmpdf2                             
00005cb1  __eqdf2                              
00005cb1  __ledf2                              
00005cb1  __ltdf2                              
00005cb1  __nedf2                              
00005d7f  dmp_set_tap_axes                     
00005de5  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005e49  SYSCFG_DL_I2C_OLED_init              
00005ead  mpu6050_i2c_sda_unlock               
00005f11  __aeabi_dcmpeq                       
00005f25  __aeabi_dcmplt                       
00005f39  __aeabi_dcmple                       
00005f4d  __aeabi_dcmpge                       
00005f61  __aeabi_dcmpgt                       
00005f75  __aeabi_fcmpeq                       
00005f89  __aeabi_fcmplt                       
00005f9d  __aeabi_fcmple                       
00005fb1  __aeabi_fcmpge                       
00005fc5  __aeabi_fcmpgt                       
00005fd9  I2C_OLED_i2c_sda_unlock              
00006039  Key_Read                             
00006099  Task_IdleFunction                    
000060f9  dmp_enable_gyro_cal                  
00006159  mpu_get_gyro_fsr                     
000061b9  DL_I2C_fillControllerTXFIFO          
00006219  SYSCFG_DL_SYSCTL_init                
00006275  Task_Tracker                         
000062d1  frexp                                
000062d1  frexpl                               
000063e5  SYSCFG_DL_I2C_MPU6050_init           
0000643d  Serial_Init                          
00006495  __TI_ltoa                            
00006545  __aeabi_idiv                         
00006545  __aeabi_idivmod                      
0000659d  Interrupt_Init                       
00006735  DL_DMA_initChannel                   
000067cd  OLED_Printf                          
00006819  __aeabi_d2iz                         
00006819  __fixdfsi                            
00006865  DL_UART_init                         
000068ad  dmp_enable_6x_lp_quat                
000068f5  dmp_enable_lp_quat                   
00006985  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000069c9  PID_IQ_SetParams                     
00006a0d  Task_Key                             
00006a51  dmp_set_shake_reject_thresh          
00006a95  dmp_set_tap_count                    
00006b1d  __aeabi_d2uiz                        
00006b1d  __fixunsdfsi                         
00006b61  __aeabi_uidiv                        
00006b61  __aeabi_uidivmod                     
00006ba1  __aeabi_f2d                          
00006ba1  __extendsfdf2                        
00006be1  atoi                                 
00006c21  vsnprintf                            
00006d91  DL_I2C_flushControllerTXFIFO         
00006e09  I2C_OLED_Set_Pos                     
00006e45  __aeabi_i2f                          
00006e45  __floatsisf                          
00006e81  __gesf2                              
00006e81  __gtsf2                              
00006ebd  __TI_auto_init_nobinit_nopinit       
00006ef9  __cmpsf2                             
00006ef9  __eqsf2                              
00006ef9  __lesf2                              
00006ef9  __ltsf2                              
00006ef9  __nesf2                              
00006f35  __muldsi3                            
00006f71  Task_LED                             
00006fa9  __aeabi_f2iz                         
00006fa9  __fixsfsi                            
0000707d  mpu_get_sample_rate                  
000070b1  dmp_set_shake_reject_time            
000070e3  dmp_set_shake_reject_timeout         
00007145  SYSCFG_DL_DMA_CH_RX_init             
00007175  _IQ24toF                             
00007205  dmp_set_tap_time                     
00007235  dmp_set_tap_time_multi               
00007265  SYSCFG_DL_init                       
000072bd  __aeabi_i2d                          
000072bd  __floatsidf                          
000072e9  vsprintf                             
00007315  PID_IQ_Init                          
000074a9  SysTick_Increasment                  
000074d1  __aeabi_ui2f                         
000074d1  __floatunsisf                        
000074f9  _c_int00_noargs                      
00007593  DL_I2C_setClockConfig                
000075dd  __aeabi_lmul                         
000075dd  __muldi3                             
00007601  memccpy                              
00007665  Delay                                
00007685  memcmp                               
000076c5  __aeabi_llsl                         
000076c5  __ashldi3                            
00007889  DL_Timer_setCaptCompUpdateMethod     
000078a5  DL_Timer_setClockConfig              
000078dd  dmp_load_motion_driver_firmware      
00007c59  DL_Timer_setCaptureCompareOutCtl     
00007d19  SYSCFG_DL_DMA_CH_TX_init             
00007d31  _IQ24div                             
00007d49  _IQ24mpy                             
00007e2b  SysGetTick                           
00007e41  __TI_zero_init_nomemset              
00007f49  __aeabi_uldivmod                     
00007f5d  dmp_register_android_orient_cb       
00007f71  dmp_register_tap_cb                  
00007f99  DL_UART_setClockConfig               
00007fab  TI_memcpy_small                      
00007fbd  __TI_decompress_none                 
00007fe1  DL_Timer_setCaptureCompareValue      
00007ff1  SYSCFG_DL_SYSTICK_init               
00008001  wcslen                               
00008011  __aeabi_memset                       
00008011  __aeabi_memset4                      
00008011  __aeabi_memset8                      
0000803b  TI_memset_small                      
00008049  SYSCFG_DL_DMA_init                   
00008055  Sys_GetTick                          
00008061  DL_Common_delayCycles                
000080f5  SysTick_Handler                      
000080fd  __aeabi_errno_addr                   
00008105  __aeabi_memcpy                       
00008105  __aeabi_memcpy4                      
00008105  __aeabi_memcpy8                      
0000812b  abort                                
00008130  C$$EXIT                              
00008131  HOSTexit                             
00008139  Reset_Handler                        
0000814d  _system_pre_init                     
00008d46  asc2_1608                            
00009336  asc2_0806                            
00009560  __aeabi_ctype_table_                 
00009560  __aeabi_ctype_table_C                
000096d0  test                                 
000096f8  reg                                  
000097c8  hw                                   
0000988c  __TI_Handler_Table_Base              
00009898  __TI_Handler_Table_Limit             
000098a0  __TI_CINIT_Base                      
000098b0  __TI_CINIT_Limit                     
000098b0  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  quat                                 
20200300  Data_Accel                           
20200306  Data_Gyro                            
2020030c  Data_Pitch                           
20200310  Data_Roll                            
20200314  Data_Yaw                             
20200318  ExISR_Flag                           
2020031c  sensor_timestamp                     
20200320  sensors                              
20200322  more                                 
20200324  Motor_Back_Left                      
20200368  Motor_Back_Right                     
202003ac  Motor_Font_Left                      
202003f0  Motor_Font_Right                     
20200460  Motor                                
20200497  Flag_LED                             
20200498  Data_MotorEncoder                    
202004a0  Data_Tracker_Input                   
202004a8  Data_Motor_TarSpeed                  
202004ac  Data_Tracker_Offset                  
202004b0  __aeabi_errno                        
202004b4  delayTick                            
202004b8  uwTick                               
202004be  Flag_MPU6050_Ready                   
202004c1  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[311 symbols]
