******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 15:53:22 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003421


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00003e48  0001c1b8  R  X
  SRAM                  20200000   00008000  00000488  00007b78  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00003e48   00003e48    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003c00   00003c00    r-x .text
  00003cc0    00003cc0    00000110   00000110    r-- .rodata
  00003dd0    00003dd0    00000078   00000078    r-- .cinit
20200000    20200000    0000028b   00000000    rw-
  20200000    20200000    00000165   00000000    rw- .data
  20200168    20200168    00000123   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003c00     
                  000000c0    00000364     libc.a : e_asin.c.obj (.text.asin)
                  00000424    000002f8            : s_atan.c.obj (.text.atan)
                  0000071c    000002bc     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000009d8    0000022c     MPU6050.o (.text.Read_Quad)
                  00000c04    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00000e30    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001024    000001b0     Task.o (.text.Task_Start)
                  000011d4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001366    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001368    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000014f0    00000170            : e_sqrt.c.obj (.text.sqrt)
                  00001660    00000168     Interrupt.o (.text.GROUP1_IRQHandler)
                  000017c8    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  000018fc    00000114     main.o (.text.main)
                  00001a10    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001b1c    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00001c24    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001d28    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001e0c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001ee8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001fc0    000000cc     Motor.o (.text.Motor_Start)
                  0000208c    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00002150    000000b8     Motor.o (.text.Motor_SetDirc)
                  00002208    000000b8     Motor.o (.text.Motor_SetDuty)
                  000022c0    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002360    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  000023ec    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00002478    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002504    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00002590    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00002614    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002698    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000271a    00000002     --HOLE-- [fill = 0]
                  0000271c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002798    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  0000280c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00002810    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002884    00000068                            : comparedf2.c.obj (.text.__ledf2)
                  000028ec    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00002950    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000029b4    00000064     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00002a18    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002a7a    00000002     --HOLE-- [fill = 0]
                  00002a7c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002ade    00000002     --HOLE-- [fill = 0]
                  00002ae0    00000060     Task_App.o (.text.Task_IdleFunction)
                  00002b40    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00002b9e    00000002     --HOLE-- [fill = 0]
                  00002ba0    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002bfc    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00002c58    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00002cb0    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00002d00    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00002d50    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00002d9c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002de8    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002e30    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00002e78    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00002ebc    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00002f00    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00002f42    00000002     --HOLE-- [fill = 0]
                  00002f44    00000040                            : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002f84    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00002fc4    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003000    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000303c    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00003078    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000030b4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000030f0    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  0000312c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003168    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000031a2    00000002     --HOLE-- [fill = 0]
                  000031a4    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000031de    00000002     --HOLE-- [fill = 0]
                  000031e0    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00003218    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000324c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003280    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  000032b0    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000032dc    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  00003306    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  0000332e    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003356    00000002     --HOLE-- [fill = 0]
                  00003358    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003380    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000033a8    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  000033d0    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  000033f8    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003420    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003448    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  0000346e    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003494    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  000034b8    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000034d8    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000034f8    00000020     SysTick.o (.text.Delay)
                  00003518    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00003536    00000002     --HOLE-- [fill = 0]
                  00003538    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00003554    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00003570    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  0000358c    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  000035a8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000035c4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000035e0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000035fc    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00003618    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00003634    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00003650    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000366c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00003688    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000036a4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000036c0    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000036dc    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000036f4    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  0000370c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003724    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  0000373c    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00003754    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  0000376c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003784    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  0000379c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000037b4    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000037cc    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000037e4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000037fc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00003814    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  0000382c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00003844    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  0000385c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00003874    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  0000388c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000038a4    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  000038bc    00000018     MPU6050.o (.text.DL_I2C_reset)
                  000038d4    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000038ec    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00003904    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  0000391c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00003934    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  0000394c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00003964    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  0000397c    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00003994    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000039ac    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000039c4    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  000039dc    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  000039f4    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00003a0c    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00003a24    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00003a3c    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00003a52    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00003a68    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00003a7e    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00003a94    00000016     SysTick.o (.text.SysGetTick)
                  00003aaa    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00003ac0    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00003ad4    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00003ae8    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00003afc    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00003b10    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00003b24    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003b38    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003b4c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00003b60    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003b74    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00003b86    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00003b98    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00003baa    00000002     --HOLE-- [fill = 0]
                  00003bac    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003bbc    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003bcc    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003bdc    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00003be8    0000000c     SysTick.o (.text.Sys_GetTick)
                  00003bf4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003bfe    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00003c08    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00003c18    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00003c22    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00003c2c    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00003c36    00000002     --HOLE-- [fill = 0]
                  00003c38    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00003c48    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00003c50    00000008     Interrupt.o (.text.SysTick_Handler)
                  00003c58    00000008     Task_App.o (.text.Task_Init)
                  00003c60    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00003c68    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003c70    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00003c76    00000002     --HOLE-- [fill = 0]
                  00003c78    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00003c88    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00003c8e    00000006            : exit.c.obj (.text:abort)
                  00003c94    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00003c98    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00003c9c    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00003ca0    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00003cb0    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00003cb4    0000000c     --HOLE-- [fill = 0]

.cinit     0    00003dd0    00000078     
                  00003dd0    00000053     (.cinit..data.load) [load image, compression = lzss]
                  00003e23    00000001     --HOLE-- [fill = 0]
                  00003e24    0000000c     (__TI_handler_table)
                  00003e30    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00003e38    00000010     (__TI_cinit_table)

.rodata    0    00003cc0    00000110     
                  00003cc0    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00003d00    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00003d28    00000028     inv_mpu.o (.rodata.test)
                  00003d50    0000001e     inv_mpu.o (.rodata.reg)
                  00003d6e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00003d70    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00003d88    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00003da0    0000000c     inv_mpu.o (.rodata.hw)
                  00003dac    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00003db6    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00003db8    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  00003dc0    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00003dc8    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  00003dcb    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00003dce    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000165     UNINITIALIZED
                  20200000    00000044     Motor.o (.data.Motor_Back_Left)
                  20200044    00000044     Motor.o (.data.Motor_Back_Right)
                  20200088    00000044     Motor.o (.data.Motor_Font_Left)
                  202000cc    00000044     Motor.o (.data.Motor_Font_Right)
                  20200110    0000002c     inv_mpu.o (.data.st)
                  2020013c    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  2020014c    00000008     Task_App.o (.data.Data_MotorEncoder)
                  20200154    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200158    00000004     SysTick.o (.data.delayTick)
                  2020015c    00000004     SysTick.o (.data.uwTick)
                  20200160    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  20200162    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  20200163    00000001     Task.o (.data.Task_Num)
                  20200164    00000001     Interrupt.o (.data.enable_group1_irq)

.bss       0    20200168    00000123     UNINITIALIZED
                  20200168    000000f0     Task.o (.bss.Task_Schedule)
                  20200258    00000010     (.common:quat)
                  20200268    00000006     (.common:Data_Accel)
                  2020026e    00000006     (.common:Data_Gyro)
                  20200274    00000004     (.common:Data_Pitch)
                  20200278    00000004     (.common:Data_Roll)
                  2020027c    00000004     (.common:Data_Yaw)
                  20200280    00000004     (.common:ExISR_Flag)
                  20200284    00000004     (.common:sensor_timestamp)
                  20200288    00000002     (.common:sensors)
                  2020028a    00000001     (.common:more)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3490    126       0      
       main.o                         276     0         0      
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3774    318       0      
                                                               
    .\APP\Src\
       Interrupt.o                    470     0         6      
       Task_App.o                     104     0         10     
    +--+------------------------------+-------+---------+---------+
       Total:                         574     0         16     
                                                               
    .\BSP\Src\
       MPU6050.o                      1884    0         47     
       Motor.o                        640     0         272    
       Task.o                         432     0         241    
       SysTick.o                      106     0         8      
       PID_IQMath.o                   110     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3172    0         568    
                                                               
    .\DMP\
       inv_mpu.o                      820     82        44     
       inv_mpu_dmp_motion_driver.o    640     0         16     
    +--+------------------------------+-------+---------+---------+
       Total:                         1460    82        60     
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1112    0         0      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2768    64        4      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2460    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       119       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   15324   583       1160   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00003e38 records: 2, size/record: 8, table size: 16
	.data: load addr=00003dd0, load size=00000053 bytes, run addr=20200000, run size=00000165 bytes, compression=lzss
	.bss: load addr=00003e30, load size=00000008 bytes, run addr=20200168, run size=00000123 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00003e24 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00001d29     00003c08     00003c06   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00003c20          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00003c2a          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00003c4e          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00003c8c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00001a11     00003c38     00003c34   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   000011df     00003c78     00003c74   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00003c9a          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003421     00003ca0     00003c9c   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[4 trampolines]
[9 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000280d  ADC0_IRQHandler                      
0000280d  ADC1_IRQHandler                      
0000280d  AES_IRQHandler                       
00003c94  C$$EXIT                              
0000280d  CANFD0_IRQHandler                    
0000280d  DAC0_IRQHandler                      
00003bf5  DL_Common_delayCycles                
00002d51  DL_DMA_initChannel                   
00002b41  DL_I2C_fillControllerTXFIFO          
0000303d  DL_I2C_flushControllerTXFIFO         
0000346f  DL_I2C_setClockConfig                
00001e0d  DL_SYSCTL_configSYSPLL               
000028ed  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002e79  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001c25  DL_Timer_initFourCCPWMMode           
00003689  DL_Timer_setCaptCompUpdateMethod     
0000397d  DL_Timer_setCaptureCompareOutCtl     
00003bbd  DL_Timer_setCaptureCompareValue      
000036a5  DL_Timer_setClockConfig              
00002de9  DL_UART_init                         
00003b75  DL_UART_setClockConfig               
0000280d  DMA_IRQHandler                       
20200268  Data_Accel                           
2020026e  Data_Gyro                            
2020014c  Data_MotorEncoder                    
20200274  Data_Pitch                           
20200278  Data_Roll                            
2020027c  Data_Yaw                             
0000280d  Default_Handler                      
000034f9  Delay                                
20200280  ExISR_Flag                           
20200162  Flag_MPU6050_Ready                   
0000280d  GROUP0_IRQHandler                    
00001661  GROUP1_IRQHandler                    
00003c95  HOSTexit                             
0000280d  HardFault_Handler                    
0000280d  I2C0_IRQHandler                      
0000280d  I2C1_IRQHandler                      
20200000  Motor_Back_Left                      
20200044  Motor_Back_Right                     
20200088  Motor_Font_Left                      
202000cc  Motor_Font_Right                     
00002151  Motor_SetDirc                        
00002209  Motor_SetDuty                        
00001fc1  Motor_Start                          
0000280d  NMI_Handler                          
000032dd  PID_IQ_Init                          
00002ebd  PID_IQ_SetParams                     
0000280d  PendSV_Handler                       
0000280d  RTC_IRQHandler                       
000009d9  Read_Quad                            
00003c9d  Reset_Handler                        
0000280d  SPI0_IRQHandler                      
0000280d  SPI1_IRQHandler                      
0000280d  SVC_Handler                          
00003281  SYSCFG_DL_DMA_CH_RX_init             
00003a25  SYSCFG_DL_DMA_CH_TX_init             
00003bdd  SYSCFG_DL_DMA_init                   
0000071d  SYSCFG_DL_GPIO_init                  
00002c59  SYSCFG_DL_I2C_MPU6050_init           
00002951  SYSCFG_DL_I2C_OLED_init              
00002361  SYSCFG_DL_MotorBack_init             
000023ed  SYSCFG_DL_MotorFront_init            
00002ba1  SYSCFG_DL_SYSCTL_init                
00003bcd  SYSCFG_DL_SYSTICK_init               
00002591  SYSCFG_DL_UART0_init                 
000032b1  SYSCFG_DL_init                       
000022c1  SYSCFG_DL_initPower                  
00003a95  SysGetTick                           
00003c51  SysTick_Handler                      
000033f9  SysTick_Increasment                  
00003be9  Sys_GetTick                          
0000280d  TIMA0_IRQHandler                     
0000280d  TIMA1_IRQHandler                     
0000280d  TIMG0_IRQHandler                     
0000280d  TIMG12_IRQHandler                    
0000280d  TIMG6_IRQHandler                     
0000280d  TIMG7_IRQHandler                     
0000280d  TIMG8_IRQHandler                     
00003b87  TI_memcpy_small                      
00002ae1  Task_IdleFunction                    
00003c59  Task_Init                            
00001025  Task_Start                           
0000280d  UART0_IRQHandler                     
0000280d  UART1_IRQHandler                     
0000280d  UART2_IRQHandler                     
0000280d  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00003e38  __TI_CINIT_Base                      
00003e48  __TI_CINIT_Limit                     
00003e48  __TI_CINIT_Warm                      
00003e24  __TI_Handler_Table_Base              
00003e30  __TI_Handler_Table_Limit             
0000312d  __TI_auto_init_nobinit_nopinit       
0000271d  __TI_decompress_lzss                 
00003b99  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003aab  __TI_zero_init_nomemset              
000011df  __adddf3                             
00001ef3  __addsf3                             
00002811  __aeabi_d2f                          
00002f01  __aeabi_d2uiz                        
000011df  __aeabi_dadd                         
00002a19  __aeabi_dcmpeq                       
00002a55  __aeabi_dcmpge                       
00002a69  __aeabi_dcmpgt                       
00002a41  __aeabi_dcmple                       
00002a2d  __aeabi_dcmplt                       
00001a11  __aeabi_ddiv                         
00001d29  __aeabi_dmul                         
000011d5  __aeabi_dsub                         
20200154  __aeabi_errno                        
00003c61  __aeabi_errno_addr                   
00002f85  __aeabi_f2d                          
000031e1  __aeabi_f2iz                         
00001ef3  __aeabi_fadd                         
00002a7d  __aeabi_fcmpeq                       
00002ab9  __aeabi_fcmpge                       
00002acd  __aeabi_fcmpgt                       
00002aa5  __aeabi_fcmple                       
00002a91  __aeabi_fcmplt                       
00002699  __aeabi_fdiv                         
00002479  __aeabi_fmul                         
00001ee9  __aeabi_fsub                         
000030b5  __aeabi_i2f                          
00001367  __aeabi_idiv0                        
00003c69  __aeabi_memcpy                       
00003c69  __aeabi_memcpy4                      
00003c69  __aeabi_memcpy8                      
00002f45  __aeabi_uidiv                        
00002f45  __aeabi_uidivmod                     
ffffffff  __binit__                            
00002885  __cmpdf2                             
00003169  __cmpsf2                             
00001a11  __divdf3                             
00002699  __divsf3                             
00002885  __eqdf2                              
00003169  __eqsf2                              
00002f85  __extendsfdf2                        
000031e1  __fixsfsi                            
00002f01  __fixunsdfsi                         
000030b5  __floatsisf                          
00002799  __gedf2                              
000030f1  __gesf2                              
00002799  __gtdf2                              
000030f1  __gtsf2                              
00002885  __ledf2                              
00003169  __lesf2                              
00002885  __ltdf2                              
00003169  __ltsf2                              
UNDEFED   __mpu_init                           
00001d29  __muldf3                             
000031a5  __muldsi3                            
00002479  __mulsf3                             
00002885  __nedf2                              
00003169  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000011d5  __subdf3                             
00001ee9  __subsf3                             
00002811  __truncdfsf2                         
00003421  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00003cb1  _system_pre_init                     
00003c8f  abort                                
000000c1  asin                                 
000000c1  asinl                                
00000425  atan                                 
00001369  atan2                                
00001369  atan2l                               
00000425  atanl                                
ffffffff  binit                                
20200158  delayTick                            
00000e31  dmp_read_fifo                        
20200164  enable_group1_irq                    
00003da0  hw                                   
00000000  interruptVectors                     
000018fd  main                                 
2020028a  more                                 
000029b5  mpu6050_i2c_sda_unlock               
00001b1d  mpu_read_fifo_stream                 
00000c05  mpu_reset_fifo                       
000017c9  mspm0_i2c_read                       
0000208d  mspm0_i2c_write                      
20200258  quat                                 
00003d50  reg                                  
20200284  sensor_timestamp                     
20200288  sensors                              
000014f1  sqrt                                 
000014f1  sqrtl                                
00003d28  test                                 
2020015c  uwTick                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  asin                                 
000000c1  asinl                                
00000200  __STACK_SIZE                         
00000425  atan                                 
00000425  atanl                                
0000071d  SYSCFG_DL_GPIO_init                  
000009d9  Read_Quad                            
00000c05  mpu_reset_fifo                       
00000e31  dmp_read_fifo                        
00001025  Task_Start                           
000011d5  __aeabi_dsub                         
000011d5  __subdf3                             
000011df  __adddf3                             
000011df  __aeabi_dadd                         
00001367  __aeabi_idiv0                        
00001369  atan2                                
00001369  atan2l                               
000014f1  sqrt                                 
000014f1  sqrtl                                
00001661  GROUP1_IRQHandler                    
000017c9  mspm0_i2c_read                       
000018fd  main                                 
00001a11  __aeabi_ddiv                         
00001a11  __divdf3                             
00001b1d  mpu_read_fifo_stream                 
00001c25  DL_Timer_initFourCCPWMMode           
00001d29  __aeabi_dmul                         
00001d29  __muldf3                             
00001e0d  DL_SYSCTL_configSYSPLL               
00001ee9  __aeabi_fsub                         
00001ee9  __subsf3                             
00001ef3  __addsf3                             
00001ef3  __aeabi_fadd                         
00001fc1  Motor_Start                          
0000208d  mspm0_i2c_write                      
00002151  Motor_SetDirc                        
00002209  Motor_SetDuty                        
000022c1  SYSCFG_DL_initPower                  
00002361  SYSCFG_DL_MotorBack_init             
000023ed  SYSCFG_DL_MotorFront_init            
00002479  __aeabi_fmul                         
00002479  __mulsf3                             
00002591  SYSCFG_DL_UART0_init                 
00002699  __aeabi_fdiv                         
00002699  __divsf3                             
0000271d  __TI_decompress_lzss                 
00002799  __gedf2                              
00002799  __gtdf2                              
0000280d  ADC0_IRQHandler                      
0000280d  ADC1_IRQHandler                      
0000280d  AES_IRQHandler                       
0000280d  CANFD0_IRQHandler                    
0000280d  DAC0_IRQHandler                      
0000280d  DMA_IRQHandler                       
0000280d  Default_Handler                      
0000280d  GROUP0_IRQHandler                    
0000280d  HardFault_Handler                    
0000280d  I2C0_IRQHandler                      
0000280d  I2C1_IRQHandler                      
0000280d  NMI_Handler                          
0000280d  PendSV_Handler                       
0000280d  RTC_IRQHandler                       
0000280d  SPI0_IRQHandler                      
0000280d  SPI1_IRQHandler                      
0000280d  SVC_Handler                          
0000280d  TIMA0_IRQHandler                     
0000280d  TIMA1_IRQHandler                     
0000280d  TIMG0_IRQHandler                     
0000280d  TIMG12_IRQHandler                    
0000280d  TIMG6_IRQHandler                     
0000280d  TIMG7_IRQHandler                     
0000280d  TIMG8_IRQHandler                     
0000280d  UART0_IRQHandler                     
0000280d  UART1_IRQHandler                     
0000280d  UART2_IRQHandler                     
0000280d  UART3_IRQHandler                     
00002811  __aeabi_d2f                          
00002811  __truncdfsf2                         
00002885  __cmpdf2                             
00002885  __eqdf2                              
00002885  __ledf2                              
00002885  __ltdf2                              
00002885  __nedf2                              
000028ed  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002951  SYSCFG_DL_I2C_OLED_init              
000029b5  mpu6050_i2c_sda_unlock               
00002a19  __aeabi_dcmpeq                       
00002a2d  __aeabi_dcmplt                       
00002a41  __aeabi_dcmple                       
00002a55  __aeabi_dcmpge                       
00002a69  __aeabi_dcmpgt                       
00002a7d  __aeabi_fcmpeq                       
00002a91  __aeabi_fcmplt                       
00002aa5  __aeabi_fcmple                       
00002ab9  __aeabi_fcmpge                       
00002acd  __aeabi_fcmpgt                       
00002ae1  Task_IdleFunction                    
00002b41  DL_I2C_fillControllerTXFIFO          
00002ba1  SYSCFG_DL_SYSCTL_init                
00002c59  SYSCFG_DL_I2C_MPU6050_init           
00002d51  DL_DMA_initChannel                   
00002de9  DL_UART_init                         
00002e79  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002ebd  PID_IQ_SetParams                     
00002f01  __aeabi_d2uiz                        
00002f01  __fixunsdfsi                         
00002f45  __aeabi_uidiv                        
00002f45  __aeabi_uidivmod                     
00002f85  __aeabi_f2d                          
00002f85  __extendsfdf2                        
0000303d  DL_I2C_flushControllerTXFIFO         
000030b5  __aeabi_i2f                          
000030b5  __floatsisf                          
000030f1  __gesf2                              
000030f1  __gtsf2                              
0000312d  __TI_auto_init_nobinit_nopinit       
00003169  __cmpsf2                             
00003169  __eqsf2                              
00003169  __lesf2                              
00003169  __ltsf2                              
00003169  __nesf2                              
000031a5  __muldsi3                            
000031e1  __aeabi_f2iz                         
000031e1  __fixsfsi                            
00003281  SYSCFG_DL_DMA_CH_RX_init             
000032b1  SYSCFG_DL_init                       
000032dd  PID_IQ_Init                          
000033f9  SysTick_Increasment                  
00003421  _c_int00_noargs                      
0000346f  DL_I2C_setClockConfig                
000034f9  Delay                                
00003689  DL_Timer_setCaptCompUpdateMethod     
000036a5  DL_Timer_setClockConfig              
0000397d  DL_Timer_setCaptureCompareOutCtl     
00003a25  SYSCFG_DL_DMA_CH_TX_init             
00003a95  SysGetTick                           
00003aab  __TI_zero_init_nomemset              
00003b75  DL_UART_setClockConfig               
00003b87  TI_memcpy_small                      
00003b99  __TI_decompress_none                 
00003bbd  DL_Timer_setCaptureCompareValue      
00003bcd  SYSCFG_DL_SYSTICK_init               
00003bdd  SYSCFG_DL_DMA_init                   
00003be9  Sys_GetTick                          
00003bf5  DL_Common_delayCycles                
00003c51  SysTick_Handler                      
00003c59  Task_Init                            
00003c61  __aeabi_errno_addr                   
00003c69  __aeabi_memcpy                       
00003c69  __aeabi_memcpy4                      
00003c69  __aeabi_memcpy8                      
00003c8f  abort                                
00003c94  C$$EXIT                              
00003c95  HOSTexit                             
00003c9d  Reset_Handler                        
00003cb1  _system_pre_init                     
00003d28  test                                 
00003d50  reg                                  
00003da0  hw                                   
00003e24  __TI_Handler_Table_Base              
00003e30  __TI_Handler_Table_Limit             
00003e38  __TI_CINIT_Base                      
00003e48  __TI_CINIT_Limit                     
00003e48  __TI_CINIT_Warm                      
20200000  Motor_Back_Left                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200044  Motor_Back_Right                     
20200088  Motor_Font_Left                      
202000cc  Motor_Font_Right                     
2020014c  Data_MotorEncoder                    
20200154  __aeabi_errno                        
20200158  delayTick                            
2020015c  uwTick                               
20200162  Flag_MPU6050_Ready                   
20200164  enable_group1_irq                    
20200258  quat                                 
20200268  Data_Accel                           
2020026e  Data_Gyro                            
20200274  Data_Pitch                           
20200278  Data_Roll                            
2020027c  Data_Yaw                             
20200280  ExISR_Flag                           
20200284  sensor_timestamp                     
20200288  sensors                              
2020028a  more                                 
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[208 symbols]
