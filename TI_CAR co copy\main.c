#include "SysConfig.h"

int main(void)
{
    SYSCFG_DL_init();
    Task_Init();


    Motor_SetDirc(&Motor_Font_Left,DIRC_FOWARD);
    Motor_SetDirc(&Motor_Font_Right,DIRC_FOWARD);
    Motor_SetDirc(&Motor_Back_Left,DIRC_FOWARD);
    Motor_SetDirc(&Motor_Back_Right,DIRC_FOWARD);
    
    Motor_SetDuty(&Motor_Font_Left,30);
    Motor_SetDuty(&Motor_Font_Right,30);
    Motor_SetDuty(&Motor_Back_Left,30);
    Motor_SetDuty(&Motor_Back_Right,30);
    delay_cycles(2);

    Motor_SetDirc(&Motor_Font_Left,DIRC_BACKWARD);
    Motor_SetDirc(&Motor_Font_Right,DIRC_BACKWARD);
    Motor_SetDirc(&Motor_Back_Left,DIRC_BACKWARD);
    Motor_SetDirc(&Motor_Back_Right,DIRC_BACKWARD);

    Motor_SetDuty(&Motor_Font_Left,-30);
    Motor_SetDuty(&Motor_Font_Right,-30);
    Motor_SetDuty(&Motor_Back_Left,-30);
    Motor_SetDuty(&Motor_Back_Right,-30);
    delay_cycles(2);

    Motor_SetDirc(&Motor_Font_Left,DIRC_NONE);
    Motor_SetDirc(&Motor_Font_Right,DIRC_NONE);
    Motor_SetDirc(&Motor_Back_Left,DIRC_NONE);
    Motor_SetDirc(&Motor_Back_Right,DIRC_NONE);

    Motor_SetDuty(&Motor_Font_Left,0);
    Motor_SetDuty(&Motor_Font_Right,0);
    Motor_SetDuty(&Motor_Back_Left,0);
    Motor_SetDuty(&Motor_Back_Right,0);



    while (1)
    {
        Task_Start(Sys_GetTick);
    }
}
