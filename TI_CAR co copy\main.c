#include "SysConfig.h"

int main(void)
{
    SYSCFG_DL_init();
    Task_Init();

    Motor_SetDuty(&Motor_Font_Left,30);
    Motor_SetDuty(&Motor_Font_Right,30);
    Motor_SetDuty(&Motor_Back_Left,30);
    Motor_SetDuty(&Motor_Back_Right,30);
    Delay(1000); // 延时1秒

    Motor_SetDuty(&Motor_Font_Left,-30);
    Motor_SetDuty(&Motor_Font_Right,-30);
    Motor_SetDuty(&Motor_Back_Left,-30);
    Motor_SetDuty(&Motor_Back_Right,-30);
    Delay(1000); // 延时1秒

    Motor_SetDuty(&Motor_Font_Left,0);
    Motor_SetDuty(&Motor_Font_Right,0);
    Motor_SetDuty(&Motor_Back_Left,0);
    Motor_SetDuty(&Motor_Back_Right,0);

    while (1)
    {
        Task_Start(Sys_GetTick);
    }
}
