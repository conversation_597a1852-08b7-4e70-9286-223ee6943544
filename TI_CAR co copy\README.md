## Example Summary

Empty project using DriverLib.
This example shows a basic empty project using DriverLib with just main file
and SysConfig initialization.

## Peripherals & Pin Assignments

| Peripheral | Pin | Function |
| --- | --- | --- |
| SYSCTL |  |  |
| DEBUGSS | PA20 | Debug Clock |
| DEBUGSS | PA19 | Debug Data In Out |

## TB6612 Motor Driver Pin Assignments

### PWM Control (Speed)
| Motor | Pin | Function |
| --- | --- | --- |
| 左前轮 | PA12 | TIMG0_CCP0 (PWMA) |
| 右前轮 | PA13 | TIMG0_CCP1 (PWMB) |
| 左后轮 | PA1 | TIMG8_CCP0 (PWMC) |
| 右后轮 | PA0 | TIMG8_CCP1 (PWMD) |

### Direction Control (IN1/IN2)
| Motor | IN1 Pin | IN2 Pin | Function |
| --- | --- | --- | --- |
| 左前轮 | PB6 | PA15 | AIN1/AIN2 |
| 右前轮 | PB7 | PA16 | BIN1/BIN2 |
| 左后轮 | PB8 | PA21 | CIN1/CIN2 |
| 右后轮 | PB9 | PA22 | DIN1/DIN2 |

### Enable Control
| Function | Pin | Description |
| --- | --- | --- |
| STBY | PA23 | TB6612使能控制(4路共用) |

## BoosterPacks, Board Resources & Jumper Settings

Visit [LP_MSPM0G3507](https://www.ti.com/tool/LP-MSPM0G3507) for LaunchPad information, including user guide and hardware files.

| Pin | Peripheral | Function | LaunchPad Pin | LaunchPad Settings |
| --- | --- | --- | --- | --- |
| PA20 | DEBUGSS | SWCLK | N/A | <ul><li>PA20 is used by SWD during debugging<br><ul><li>`J101 15:16 ON` Connect to XDS-110 SWCLK while debugging<br><li>`J101 15:16 OFF` Disconnect from XDS-110 SWCLK if using pin in application</ul></ul> |
| PA19 | DEBUGSS | SWDIO | N/A | <ul><li>PA19 is used by SWD during debugging<br><ul><li>`J101 13:14 ON` Connect to XDS-110 SWDIO while debugging<br><li>`J101 13:14 OFF` Disconnect from XDS-110 SWDIO if using pin in application</ul></ul> |

### Device Migration Recommendations
This project was developed for a superset device included in the LP_MSPM0G3507 LaunchPad. Please
visit the [CCS User's Guide](https://software-dl.ti.com/msp430/esd/MSPM0-SDK/latest/docs/english/tools/ccs_ide_guide/doc_guide/doc_guide-srcs/ccs_ide_guide.html#sysconfig-project-migration)
for information about migrating to other MSPM0 devices.

### Low-Power Recommendations
TI recommends to terminate unused pins by setting the corresponding functions to
GPIO and configure the pins to output low or input with internal
pullup/pulldown resistor.

SysConfig allows developers to easily configure unused pins by selecting **Board**→**Configure Unused Pins**.

For more information about jumper configuration to achieve low-power using the
MSPM0 LaunchPad, please visit the [LP-MSPM0G3507 User's Guide](https://www.ti.com/lit/slau873).

## TB6612 Motor Driver Usage

### Hardware Setup
1. 按照上述引脚分配表连接TB6612模块
2. 确保TB6612的电源连接正确(VCC=3.3V, VM=6-15V)
3. 连接四个电机到TB6612的输出端

### Software Usage
```c
// 初始化电机系统
Motor_Start();

// 使能/禁用TB6612
Motor_Enable(true);  // 使能
Motor_Enable(false); // 禁用

// 设置电机速度 (-100 ~ +100)
Motor_SetDuty(&Motor_Font_Left, 50.0f);   // 左前轮50%正转
Motor_SetDuty(&Motor_Font_Right, -30.0f); // 右前轮30%反转
Motor_SetDuty(&Motor_Back_Left, 0.0f);    // 左后轮停止(制动)

// 测试所有电机
Motor_Test_TB6612(); // 所有电机30%正转
```

### Control Logic
- **正值**: 电机正转 (IN1=0, IN2=1)
- **负值**: 电机反转 (IN1=1, IN2=0)
- **零值**: 电机制动 (IN1=0, IN2=0)
- **STBY=0**: 所有电机停止

## 程序运行逻辑

程序启动后会自动执行以下动作序列：
1. **前进1秒**: 所有电机30%功率正转，持续1000ms
2. **后退1秒**: 所有电机30%功率反转，持续1000ms
3. **停止**: 所有电机停止并进入任务调度循环

### 延时函数说明
- `Delay(1000)`: 毫秒级精确延时，基于SysTick中断实现
- `delay_cycles(n)`: CPU周期级延时，仅用于硬件初始化

## Example Usage

Compile, load and run the example.
